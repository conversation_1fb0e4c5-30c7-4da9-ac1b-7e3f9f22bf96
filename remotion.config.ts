import { Config } from '@remotion/cli/config';

Config.setVideoImageFormat('jpeg');
Config.setOverwriteOutput(true);
Config.setPixelFormat('yuv420p');
Config.setCodec('h264');
Config.setCrf(18);
Config.setImageSequence(false);

// Set the entry point for Remotion
Config.setEntryPoint('./src/remotion/Root.tsx');

// Configure output directory
Config.setOutputLocation('./out/video.mp4');

// Set browser executable path if needed
// Config.setBrowserExecutable('/path/to/browser');

export default Config;
