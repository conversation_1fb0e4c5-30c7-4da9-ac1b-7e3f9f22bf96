import React from 'react';
import { AbsoluteFill, Video, useCurrentFrame, useVideoConfig } from 'remotion';
import type { Timeline, VideoFile } from '../types/video';

interface VideoCompositionProps {
  timeline: Timeline;
  videoFiles: VideoFile[];
}

export const VideoComposition: React.FC<VideoCompositionProps> = ({
  timeline,
  videoFiles
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // Convert frame to time in seconds
  const currentTime = frame / fps;

  // Get all clips that should be visible at the current time
  const getVisibleClips = () => {
    const visibleClips: Array<{
      clip: any;
      videoFile: VideoFile | undefined;
      trackIndex: number;
    }> = [];

    timeline.tracks.forEach((track, trackIndex) => {
      if (!track.visible) return;

      track.clips.forEach(clip => {
        const clipStart = clip.position;
        const clipEnd = clip.position + clip.duration;

        if (currentTime >= clipStart && currentTime < clipEnd) {
          const videoFile = videoFiles.find(file => file.id === clip.videoFileId);
          visibleClips.push({ clip, videoFile, trackIndex });
        }
      });
    });

    return visibleClips;
  };

  const visibleClips = getVisibleClips();

  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>
      {visibleClips.map(({ clip, videoFile, trackIndex }) => {
        if (!videoFile) return null;

        // Calculate the video time based on clip timing
        const relativeTime = currentTime - clip.position;
        const videoTime = clip.startTime + relativeTime;
        const videoFrame = Math.floor(videoTime * fps);

        return (
          <AbsoluteFill key={`${clip.id}-${trackIndex}`}>
            <Video
              src={videoFile.url}
              startFrom={Math.floor(clip.startTime * fps)}
              endAt={Math.floor((clip.startTime + clip.duration) * fps)}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
              }}
            />
          </AbsoluteFill>
        );
      })}
    </AbsoluteFill>
  );
};
