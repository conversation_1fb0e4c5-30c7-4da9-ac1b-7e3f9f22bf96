import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Film, AlertCircle } from 'lucide-react';
import type { VideoFile } from '../types/video';
import { getVideoMetadata, generateVideoThumbnails, isVideoFile, formatFileSize } from '../utils/videoUtils';
import { v4 as uuidv4 } from 'uuid';

interface VideoUploadProps {
  onVideoUpload: (videoFile: VideoFile) => void;
  className?: string;
}

export const VideoUpload: React.FC<VideoUploadProps> = ({ onVideoUpload, className = '' }) => {
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const processVideoFile = useCallback(async (file: File) => {
    try {
      setIsProcessing(true);
      setError(null);

      // Get video metadata
      const metadata = await getVideoMetadata(file);
      
      // Generate thumbnails
      const thumbnails = await generateVideoThumbnails(file, 20);
      
      // Create video file object
      const videoFile: VideoFile = {
        id: uuidv4(),
        file,
        name: file.name,
        duration: metadata.duration,
        width: metadata.width,
        height: metadata.height,
        frameRate: metadata.frameRate,
        size: file.size,
        url: URL.createObjectURL(file),
        thumbnails,
        createdAt: new Date(),
      };

      onVideoUpload(videoFile);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process video file');
    } finally {
      setIsProcessing(false);
    }
  }, [onVideoUpload]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    for (const file of acceptedFiles) {
      if (isVideoFile(file)) {
        await processVideoFile(file);
      } else {
        setError(`${file.name} is not a supported video format`);
      }
    }
  }, [processVideoFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm']
    },
    multiple: true,
  });

  return (
    <div className={`w-full ${className}`}>
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive 
            ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
          ${isProcessing ? 'pointer-events-none opacity-50' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              <p className="text-gray-600 dark:text-gray-300">Processing video...</p>
            </>
          ) : (
            <>
              <div className="flex items-center justify-center w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full">
                {isDragActive ? (
                  <Upload className="w-8 h-8 text-blue-500" />
                ) : (
                  <Film className="w-8 h-8 text-gray-500" />
                )}
              </div>
              
              <div>
                <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {isDragActive ? 'Drop videos here' : 'Upload video files'}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Drag and drop or click to select MP4, MOV, AVI, MKV, WebM files
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
          </div>
        </div>
      )}
    </div>
  );
};
