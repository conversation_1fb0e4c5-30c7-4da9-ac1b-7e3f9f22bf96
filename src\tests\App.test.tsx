import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from '../App';

describe('Video Editor App', () => {
  it('renders the main application', () => {
    render(<App />);
    
    // Check if the main title is present
    expect(screen.getByText('Video Editor Pro')).toBeInTheDocument();
    
    // Check if the upload tab is present
    expect(screen.getByText('Upload')).toBeInTheDocument();
    
    // Check if the library tab is present
    expect(screen.getByText('Library')).toBeInTheDocument();
    
    // Check if the timeline is present
    expect(screen.getByText('Timeline')).toBeInTheDocument();
  });

  it('shows upload interface by default', () => {
    render(<App />);
    
    // Check if upload text is visible
    expect(screen.getByText('Upload video files')).toBeInTheDocument();
  });

  it('displays empty timeline message when no clips are present', () => {
    render(<App />);
    
    // Check if empty timeline message is shown
    expect(screen.getByText('Timeline is empty')).toBeInTheDocument();
  });
});
