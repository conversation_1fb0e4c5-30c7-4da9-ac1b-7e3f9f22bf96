import React from 'react';
import type { TimelineTrack as TimelineTrackType, VideoClip, VideoFile } from '../../types/video';
import { VideoClipComponent } from './VideoClipComponent';
import { Eye, EyeOff, Lock, Unlock } from 'lucide-react';

interface TimelineTrackProps {
  track: TimelineTrackType;
  videoFiles: VideoFile[];
  pixelsPerSecond: number;
  currentTime: number;
  selectedClips: string[];
  onClipSelect: (clipId: string, multiSelect?: boolean) => void;
  onClipUpdate: (clipId: string, updates: Partial<VideoClip>) => void;
  onClipDelete: (clipId: string) => void;
  onTrackToggleVisibility: (trackId: string) => void;
  onTrackToggleLock: (trackId: string) => void;
  className?: string;
}

export const TimelineTrackComponent: React.FC<TimelineTrackProps> = ({
  track,
  videoFiles,
  pixelsPerSecond,
  currentTime,
  selectedClips,
  onClipSelect,
  onClipUpdate,
  onClipDelete,
  onTrackToggleVisibility,
  onTrackToggleLock,
  className = ''
}) => {
  const trackRef = React.useRef<HTMLDivElement>(null);

  const handleTrackClick = (e: React.MouseEvent) => {
    // Only handle clicks on empty areas of the track
    if (e.target === trackRef.current) {
      // Clear selection when clicking on empty track area
      onClipSelect('', false);
    }
  };

  return (
    <div className={`flex border-b border-gray-700 ${className}`}>
      {/* Track Header */}
      <div className="w-48 bg-gray-800 border-r border-gray-700 p-3 flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-200 truncate">
            {track.name}
          </h4>
          <p className="text-xs text-gray-400 mt-1">
            {track.clips.length} clip{track.clips.length !== 1 ? 's' : ''}
          </p>
        </div>
        
        <div className="flex items-center space-x-1 ml-2">
          <button
            onClick={() => onTrackToggleVisibility(track.id)}
            className={`p-1 rounded transition-colors ${
              track.visible
                ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                : 'text-gray-500 hover:text-gray-400 hover:bg-gray-700'
            }`}
            title={track.visible ? 'Hide track' : 'Show track'}
          >
            {track.visible ? (
              <Eye className="w-4 h-4" />
            ) : (
              <EyeOff className="w-4 h-4" />
            )}
          </button>
          
          <button
            onClick={() => onTrackToggleLock(track.id)}
            className={`p-1 rounded transition-colors ${
              track.locked
                ? 'text-red-400 hover:text-red-300 hover:bg-gray-700'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            }`}
            title={track.locked ? 'Unlock track' : 'Lock track'}
          >
            {track.locked ? (
              <Lock className="w-4 h-4" />
            ) : (
              <Unlock className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* Track Content */}
      <div className="flex-1 relative">
        <div
          ref={trackRef}
          className="relative bg-gray-900 hover:bg-gray-850 transition-colors cursor-pointer"
          style={{ height: track.height, minHeight: '60px' }}
          onClick={handleTrackClick}
        >
          {/* Track background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div
              className="h-full"
              style={{
                backgroundImage: `repeating-linear-gradient(
                  90deg,
                  transparent,
                  transparent ${pixelsPerSecond - 1}px,
                  rgba(255,255,255,0.1) ${pixelsPerSecond - 1}px,
                  rgba(255,255,255,0.1) ${pixelsPerSecond}px
                )`
              }}
            />
          </div>

          {/* Current time indicator */}
          <div
            className="absolute top-0 w-0.5 h-full bg-red-500 opacity-50 pointer-events-none z-10"
            style={{ left: currentTime * pixelsPerSecond }}
          />

          {/* Video clips */}
          {track.clips.map((clip) => {
            const videoFile = videoFiles.find(file => file.id === clip.videoFileId);
            return (
              <VideoClipComponent
                key={clip.id}
                clip={clip}
                videoFile={videoFile}
                pixelsPerSecond={pixelsPerSecond}
                isSelected={selectedClips.includes(clip.id)}
                isTrackLocked={track.locked}
                onSelect={(multiSelect) => onClipSelect(clip.id, multiSelect)}
                onUpdate={(updates) => onClipUpdate(clip.id, updates)}
                onDelete={() => onClipDelete(clip.id)}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};
