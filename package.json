{"name": "augmentremotion", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "remotion:studio": "remotion studio", "remotion:render": "remotion render"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@remotion/bundler": "^4.0.321", "@remotion/cli": "^4.0.321", "@remotion/lambda": "^4.0.321", "@remotion/player": "^4.0.321", "@remotion/renderer": "^4.0.321", "@remotion/studio": "^4.0.321", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "tailwindcss": "^4.1.11", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^26.1.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vitest": "^3.2.4"}}