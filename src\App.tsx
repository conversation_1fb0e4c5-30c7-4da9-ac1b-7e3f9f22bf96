import React from 'react';
import { VideoUpload } from './components/VideoUpload';
import { VideoLibrary } from './components/VideoLibrary';
import { VideoPreview } from './components/VideoPreview';
import { Timeline } from './components/Timeline/Timeline';
import { EditingToolbar } from './components/EditingToolbar';
import { ExportDialog } from './components/ExportDialog';
import { ToastContainer } from './components/Toast';
import { useVideoEditor } from './hooks/useVideoEditor';
import { useToast } from './hooks/useToast';
import { Film, Upload, Library, Download } from 'lucide-react';

function App() {
  const {
    videoFiles,
    timeline,
    selectedClips,
    isPlaying,
    addVideoFile,
    removeVideoFile,
    addClipToTimeline,
    updateClip,
    deleteClip,
    setCurrentTime,
    play,
    pause,
    togglePlayback,
    updateTimeline,
    setSelectedClips,
    cutAtPlayhead,
    deleteSelectedClips,
    copySelectedClips,
    undo,
    redo,
    saveProject,
    exportVideo,
    canUndo,
    canRedo,
    isExporting,
    exportProgress,
  } = useVideoEditor();

  const [activeTab, setActiveTab] = React.useState<'upload' | 'library'>('upload');
  const [showExportDialog, setShowExportDialog] = React.useState(false);
  const { toasts, removeToast, success, info } = useToast();

  // Enhanced handlers with toast notifications
  const handleVideoUpload = (videoFile: any) => {
    addVideoFile(videoFile);
    success('Video uploaded', `${videoFile.name} has been added to your library`);
  };

  const handleVideoRemove = (fileId: string) => {
    removeVideoFile(fileId);
    info('Video removed', 'Video has been removed from your library');
  };

  const handleClipAdd = (fileId: string) => {
    addClipToTimeline(fileId);
    success('Clip added', 'Video clip has been added to the timeline');
  };

  const handleExport = (settings: any) => {
    exportVideo(settings);
    info('Export started', 'Your video is being exported...');
    setShowExportDialog(false);
  };

  const handleTimeUpdate = (time: number) => {
    setCurrentTime(time);
  };

  const handleSeek = (time: number) => {
    setCurrentTime(time);
  };

  const handleTimelineUpdate = (updates: Partial<typeof timeline>) => {
    updateTimeline(updates);
  };

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent shortcuts when typing in inputs
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case ' ':
          e.preventDefault();
          togglePlayback();
          break;
        case 'c':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            copySelectedClips();
          } else {
            e.preventDefault();
            cutAtPlayhead();
          }
          break;
        case 'delete':
        case 'backspace':
          e.preventDefault();
          deleteSelectedClips();
          break;
        case 'z':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            if (e.shiftKey) {
              redo();
            } else {
              undo();
            }
          }
          break;
        case 'y':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            redo();
          }
          break;
        case 's':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            saveProject();
          }
          break;

      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [togglePlayback, cutAtPlayhead, deleteSelectedClips, undo, redo, saveProject, copySelectedClips]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Film className="w-8 h-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Video Editor Pro
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {videoFiles.length} videos • {timeline.tracks.reduce((acc, track) => acc + track.clips.length, 0)} clips
              </span>

              <button
                onClick={() => setShowExportDialog(true)}
                disabled={timeline.tracks.every(track => track.clips.length === 0)}
                className="flex items-center space-x-2 px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Sidebar */}
        <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          {/* Tab Navigation */}
          <div className="flex border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setActiveTab('upload')}
              className={`flex-1 px-4 py-3 text-sm font-medium flex items-center justify-center space-x-2 ${
                activeTab === 'upload'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Upload className="w-4 h-4" />
              <span>Upload</span>
            </button>

            <button
              onClick={() => setActiveTab('library')}
              className={`flex-1 px-4 py-3 text-sm font-medium flex items-center justify-center space-x-2 ${
                activeTab === 'library'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Library className="w-4 h-4" />
              <span>Library</span>
            </button>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-y-auto">
            {activeTab === 'upload' ? (
              <div className="p-4">
                <VideoUpload onVideoUpload={handleVideoUpload} />
              </div>
            ) : (
              <VideoLibrary
                videoFiles={videoFiles}
                onRemoveVideo={handleVideoRemove}
                onAddToTimeline={handleClipAdd}
              />
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Editing Toolbar */}
          <EditingToolbar
            timeline={timeline}
            selectedClips={selectedClips}
            onCutAtPlayhead={cutAtPlayhead}
            onSplitClip={(_clipId) => {
              // Split at current playhead position
              cutAtPlayhead();
              success('Clip split', 'Video clip has been split at the playhead position');
            }}
            onDeleteSelected={deleteSelectedClips}
            onCopySelected={copySelectedClips}
            onUndo={undo}
            onRedo={redo}
            onSave={saveProject}
            canUndo={canUndo}
            canRedo={canRedo}
          />

          {/* Video Preview */}
          <div className="flex-1 p-6">
            <VideoPreview
              timeline={timeline}
              videoFiles={videoFiles}
              isPlaying={isPlaying}
              onPlay={play}
              onPause={pause}
              onSeek={handleSeek}
              onTimeUpdate={handleTimeUpdate}
              className="h-full"
            />
          </div>

          {/* Timeline */}
          <div className="h-80 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
            <Timeline
              timeline={timeline}
              videoFiles={videoFiles}
              selectedClips={selectedClips}
              onTimelineUpdate={handleTimelineUpdate}
              onClipUpdate={updateClip}
              onClipDelete={deleteClip}
              onSeek={handleSeek}
              onSelectedClipsChange={setSelectedClips}
              className="h-full"
            />
          </div>
        </div>
      </div>

      {/* Export Dialog */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        timeline={timeline}
        videoFiles={videoFiles}
        onExport={handleExport}
        isExporting={isExporting}
        exportProgress={exportProgress}
      />

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}

export default App;
