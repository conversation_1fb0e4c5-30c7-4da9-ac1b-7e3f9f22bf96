import { useState, useCallback, useRef } from 'react';
import type { VideoFile, VideoClip, Timeline, TimelineTrack, EditOperation, ExportSettings } from '../types/video';
import { v4 as uuidv4 } from 'uuid';

export const useVideoEditor = () => {
  const [videoFiles, setVideoFiles] = useState<VideoFile[]>([]);
  const [timeline, setTimeline] = useState<Timeline>({
    tracks: [
      {
        id: uuidv4(),
        name: 'Video Track 1',
        clips: [],
        height: 80,
        locked: false,
        visible: true,
      }
    ],
    duration: 0,
    currentTime: 0,
    zoom: 1,
    pixelsPerSecond: 50,
  });
  const [selectedClips, setSelectedClips] = useState<string[]>([]);
  const [editHistory, setEditHistory] = useState<EditOperation[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [previewRef, setPreviewRef] = useState<HTMLVideoElement | null>(null);
  const [clipboard, setClipboard] = useState<VideoClip[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const addVideoFile = useCallback((videoFile: VideoFile) => {
    setVideoFiles(prev => [...prev, videoFile]);
  }, []);

  const removeVideoFile = useCallback((fileId: string) => {
    setVideoFiles(prev => prev.filter(file => file.id !== fileId));
    // Also remove any clips using this file
    setTimeline(prev => ({
      ...prev,
      tracks: prev.tracks.map(track => ({
        ...track,
        clips: track.clips.filter(clip => clip.videoFileId !== fileId)
      }))
    }));
  }, []);

  const addClipToTimeline = useCallback((videoFileId: string, trackIndex: number = 0) => {
    const videoFile = videoFiles.find(file => file.id === videoFileId);
    if (!videoFile) return;

    const newClip: VideoClip = {
      id: uuidv4(),
      videoFileId,
      name: videoFile.name,
      startTime: 0,
      endTime: videoFile.duration,
      trimStart: 0,
      trimEnd: videoFile.duration,
      position: timeline.duration,
      trackIndex,
      duration: videoFile.duration,
      originalDuration: videoFile.duration,
      thumbnails: videoFile.thumbnails,
    };

    setTimeline(prev => {
      const newTracks = [...prev.tracks];
      if (newTracks[trackIndex]) {
        newTracks[trackIndex] = {
          ...newTracks[trackIndex],
          clips: [...newTracks[trackIndex].clips, newClip]
        };
      }

      return {
        ...prev,
        tracks: newTracks,
        duration: Math.max(prev.duration, newClip.position + newClip.duration)
      };
    });
  }, [videoFiles, timeline.duration]);

  const updateClip = useCallback((clipId: string, updates: Partial<VideoClip>) => {
    setTimeline(prev => ({
      ...prev,
      tracks: prev.tracks.map(track => ({
        ...track,
        clips: track.clips.map(clip => 
          clip.id === clipId ? { ...clip, ...updates } : clip
        )
      }))
    }));
  }, []);

  const deleteClip = useCallback((clipId: string) => {
    setTimeline(prev => ({
      ...prev,
      tracks: prev.tracks.map(track => ({
        ...track,
        clips: track.clips.filter(clip => clip.id !== clipId)
      }))
    }));
    setSelectedClips(prev => prev.filter(id => id !== clipId));
  }, []);

  const splitClip = useCallback((clipId: string, splitTime: number) => {
    const clip = timeline.tracks
      .flatMap(track => track.clips)
      .find(c => c.id === clipId);
    
    if (!clip) return;

    const relativeTime = splitTime - clip.position;
    if (relativeTime <= 0 || relativeTime >= clip.duration) return;

    const firstClip: VideoClip = {
      ...clip,
      id: uuidv4(),
      endTime: clip.startTime + relativeTime,
      duration: relativeTime,
    };

    const secondClip: VideoClip = {
      ...clip,
      id: uuidv4(),
      startTime: clip.startTime + relativeTime,
      position: clip.position + relativeTime,
      duration: clip.duration - relativeTime,
    };

    setTimeline(prev => ({
      ...prev,
      tracks: prev.tracks.map(track => ({
        ...track,
        clips: track.clips.flatMap(c => 
          c.id === clipId ? [firstClip, secondClip] : [c]
        )
      }))
    }));
  }, [timeline.tracks]);

  const setCurrentTime = useCallback((time: number) => {
    setTimeline(prev => ({
      ...prev,
      currentTime: Math.max(0, Math.min(time, prev.duration))
    }));
  }, []);

  const play = useCallback(() => {
    setIsPlaying(true);
  }, []);

  const pause = useCallback(() => {
    setIsPlaying(false);
  }, []);

  const togglePlayback = useCallback(() => {
    setIsPlaying(prev => !prev);
  }, []);

  const setZoom = useCallback((zoom: number) => {
    setTimeline(prev => ({
      ...prev,
      zoom: Math.max(0.1, Math.min(zoom, 10)),
      pixelsPerSecond: 50 * zoom
    }));
  }, []);

  const updateTimeline = useCallback((updates: Partial<Timeline>) => {
    setTimeline(prev => ({ ...prev, ...updates }));
  }, []);

  const addToHistory = useCallback((operation: EditOperation) => {
    setEditHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(operation);
      return newHistory;
    });
    setHistoryIndex(prev => prev + 1);
  }, [historyIndex]);

  const cutAtPlayhead = useCallback(() => {
    const currentTime = timeline.currentTime;

    // Find clips that intersect with the playhead
    const affectedClips: { clip: VideoClip; trackIndex: number }[] = [];

    timeline.tracks.forEach((track, trackIndex) => {
      track.clips.forEach(clip => {
        if (currentTime > clip.position && currentTime < clip.position + clip.duration) {
          affectedClips.push({ clip, trackIndex });
        }
      });
    });

    if (affectedClips.length === 0) return;

    // Split each affected clip
    affectedClips.forEach(({ clip, trackIndex }) => {
      splitClip(clip.id, currentTime);
    });

    addToHistory({
      id: uuidv4(),
      type: 'cut',
      clipId: '',
      timestamp: currentTime,
      data: { affectedClips }
    });
  }, [timeline.currentTime, timeline.tracks, splitClip, addToHistory]);

  const deleteSelectedClips = useCallback(() => {
    if (selectedClips.length === 0) return;

    const deletedClips: VideoClip[] = [];

    setTimeline(prev => ({
      ...prev,
      tracks: prev.tracks.map(track => ({
        ...track,
        clips: track.clips.filter(clip => {
          if (selectedClips.includes(clip.id)) {
            deletedClips.push(clip);
            return false;
          }
          return true;
        })
      }))
    }));

    setSelectedClips([]);

    addToHistory({
      id: uuidv4(),
      type: 'delete',
      clipId: '',
      timestamp: Date.now(),
      data: { deletedClips }
    });
  }, [selectedClips, addToHistory]);

  const copySelectedClips = useCallback(() => {
    if (selectedClips.length === 0) return;

    const clipsToCopy: VideoClip[] = [];

    timeline.tracks.forEach(track => {
      track.clips.forEach(clip => {
        if (selectedClips.includes(clip.id)) {
          clipsToCopy.push({ ...clip });
        }
      });
    });

    setClipboard(clipsToCopy);
  }, [selectedClips, timeline.tracks]);

  const undo = useCallback(() => {
    if (historyIndex < 0) return;

    const operation = editHistory[historyIndex];

    // Implement undo logic based on operation type
    switch (operation.type) {
      case 'delete':
        // Restore deleted clips
        if (operation.data.deletedClips) {
          setTimeline(prev => ({
            ...prev,
            tracks: prev.tracks.map(track => ({
              ...track,
              clips: [...track.clips, ...operation.data.deletedClips.filter((clip: VideoClip) => clip.trackIndex === prev.tracks.indexOf(track))]
            }))
          }));
        }
        break;
      // Add more undo cases as needed
    }

    setHistoryIndex(prev => prev - 1);
  }, [historyIndex, editHistory]);

  const redo = useCallback(() => {
    if (historyIndex >= editHistory.length - 1) return;

    const operation = editHistory[historyIndex + 1];

    // Implement redo logic based on operation type
    switch (operation.type) {
      case 'delete':
        // Re-delete clips
        if (operation.data.deletedClips) {
          const clipIds = operation.data.deletedClips.map((clip: VideoClip) => clip.id);
          setTimeline(prev => ({
            ...prev,
            tracks: prev.tracks.map(track => ({
              ...track,
              clips: track.clips.filter(clip => !clipIds.includes(clip.id))
            }))
          }));
        }
        break;
      // Add more redo cases as needed
    }

    setHistoryIndex(prev => prev + 1);
  }, [historyIndex, editHistory]);

  const saveProject = useCallback(() => {
    // Implement project saving logic
    const projectData = {
      timeline,
      videoFiles: videoFiles.map(file => ({
        ...file,
        file: undefined, // Don't serialize the File object
      })),
      editHistory,
      version: '1.0.0',
      savedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(projectData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'video-project.json';
    link.click();

    URL.revokeObjectURL(url);
  }, [timeline, videoFiles, editHistory]);

  const exportVideo = useCallback(async (settings: ExportSettings) => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // In a real implementation, this would use Remotion's rendering API
      // For now, we'll simulate the export process

      const totalFrames = Math.ceil(timeline.duration * settings.frameRate);

      // Simulate export progress
      for (let frame = 0; frame <= totalFrames; frame += Math.ceil(totalFrames / 100)) {
        await new Promise(resolve => setTimeout(resolve, 50));
        setExportProgress((frame / totalFrames) * 100);
      }

      // Create a simple project file for now
      const exportData = {
        timeline,
        videoFiles: videoFiles.map(file => ({
          ...file,
          file: undefined, // Don't serialize the File object
        })),
        settings,
        exportedAt: new Date().toISOString()
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `video-export-${Date.now()}.json`;
      link.click();

      URL.revokeObjectURL(url);

      setExportProgress(100);

      // In a real implementation, you would integrate with Remotion's render API:
      // import { renderMedia } from '@remotion/renderer';
      // const result = await renderMedia({
      //   composition: 'VideoEditor',
      //   serveUrl: 'http://localhost:3000',
      //   codec: 'h264',
      //   outputLocation: 'out/video.mp4',
      //   inputProps: { timeline, videoFiles }
      // });

    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(0);
      }, 1000);
    }
  }, [timeline, videoFiles]);

  return {
    // State
    videoFiles,
    timeline,
    selectedClips,
    editHistory,
    isPlaying,
    previewRef,
    clipboard,
    isExporting,
    exportProgress,

    // Actions
    addVideoFile,
    removeVideoFile,
    addClipToTimeline,
    updateClip,
    deleteClip,
    splitClip,
    setCurrentTime,
    play,
    pause,
    togglePlayback,
    setZoom,
    updateTimeline,
    setSelectedClips,
    setPreviewRef,

    // Editing operations
    cutAtPlayhead,
    deleteSelectedClips,
    copySelectedClips,
    undo,
    redo,
    saveProject,
    exportVideo,

    // History state
    canUndo: historyIndex >= 0,
    canRedo: historyIndex < editHistory.length - 1,
  };
};
