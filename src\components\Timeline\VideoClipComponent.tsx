import React from 'react';
import type { VideoClip, VideoFile } from '../../types/video';
import { formatTime } from '../../utils/videoUtils';
import { Scissors, Trash2 } from 'lucide-react';

interface VideoClipComponentProps {
  clip: VideoClip;
  videoFile?: VideoFile;
  pixelsPerSecond: number;
  isSelected: boolean;
  isTrackLocked: boolean;
  onSelect: (multiSelect?: boolean) => void;
  onUpdate: (updates: Partial<VideoClip>) => void;
  onDelete: () => void;
  className?: string;
}

export const VideoClipComponent: React.FC<VideoClipComponentProps> = ({
  clip,
  videoFile,
  pixelsPerSecond,
  isSelected,
  isTrackLocked,
  onSelect,
  onUpdate,
  onDelete,
  className = ''
}) => {
  const clipRef = React.useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = React.useState(false);
  const [isResizing, setIsResizing] = React.useState<'left' | 'right' | null>(null);
  const [dragStart, setDragStart] = React.useState({ x: 0, position: 0 });

  const clipWidth = clip.duration * pixelsPerSecond;
  const clipLeft = clip.position * pixelsPerSecond;

  const handleMouseDown = (e: React.MouseEvent) => {
    if (isTrackLocked) return;
    
    e.stopPropagation();
    
    const rect = clipRef.current?.getBoundingClientRect();
    if (!rect) return;

    const relativeX = e.clientX - rect.left;
    const isNearLeftEdge = relativeX < 8;
    const isNearRightEdge = relativeX > rect.width - 8;

    if (isNearLeftEdge) {
      setIsResizing('left');
    } else if (isNearRightEdge) {
      setIsResizing('right');
    } else {
      setIsDragging(true);
      setDragStart({
        x: e.clientX,
        position: clip.position
      });
    }

    onSelect(e.ctrlKey || e.metaKey);
  };

  const handleMouseMove = React.useCallback((e: MouseEvent) => {
    if (isTrackLocked) return;

    if (isDragging) {
      const deltaX = e.clientX - dragStart.x;
      const deltaTime = deltaX / pixelsPerSecond;
      const newPosition = Math.max(0, dragStart.position + deltaTime);
      
      onUpdate({ position: newPosition });
    } else if (isResizing) {
      const rect = clipRef.current?.getBoundingClientRect();
      if (!rect) return;

      if (isResizing === 'left') {
        const newStartTime = Math.max(0, (e.clientX - rect.left) / pixelsPerSecond);
        const newDuration = clip.duration - newStartTime;
        if (newDuration > 0.1) {
          onUpdate({
            trimStart: clip.trimStart + newStartTime,
            duration: newDuration,
            position: clip.position + newStartTime
          });
        }
      } else if (isResizing === 'right') {
        const newDuration = Math.max(0.1, (e.clientX - rect.left) / pixelsPerSecond);
        onUpdate({
          trimEnd: clip.trimStart + newDuration,
          duration: newDuration
        });
      }
    }
  }, [isDragging, isResizing, dragStart, pixelsPerSecond, clip, onUpdate, isTrackLocked]);

  const handleMouseUp = React.useCallback(() => {
    setIsDragging(false);
    setIsResizing(null);
  }, []);

  React.useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Open clip properties dialog
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  };

  return (
    <div
      ref={clipRef}
      className={`
        absolute top-1 bottom-1 rounded cursor-pointer select-none transition-all
        ${isSelected 
          ? 'ring-2 ring-blue-400 shadow-lg' 
          : 'hover:shadow-md'
        }
        ${isTrackLocked ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      style={{
        left: clipLeft,
        width: clipWidth,
        minWidth: '20px'
      }}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
    >
      {/* Clip background with thumbnail */}
      <div className="relative w-full h-full bg-gradient-to-r from-blue-600 to-blue-700 rounded overflow-hidden">
        {/* Thumbnail strip */}
        {videoFile?.thumbnails && (
          <div className="absolute inset-0 flex">
            {videoFile.thumbnails.slice(0, Math.ceil(clipWidth / 40)).map((thumbnail, index) => (
              <img
                key={index}
                src={thumbnail}
                alt=""
                className="h-full object-cover opacity-60"
                style={{ width: '40px', minWidth: '40px' }}
              />
            ))}
          </div>
        )}

        {/* Clip overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-20" />

        {/* Clip content */}
        <div className="relative h-full p-2 flex flex-col justify-between text-white text-xs">
          <div className="flex items-start justify-between">
            <span className="font-medium truncate flex-1">
              {clip.name}
            </span>
            
            {isSelected && !isTrackLocked && (
              <button
                onClick={handleDelete}
                className="ml-1 p-1 hover:bg-red-600 rounded transition-colors"
                title="Delete clip"
              >
                <Trash2 className="w-3 h-3" />
              </button>
            )}
          </div>
          
          <div className="text-xs opacity-75">
            {formatTime(clip.duration)}
          </div>
        </div>

        {/* Resize handles */}
        {isSelected && !isTrackLocked && (
          <>
            <div className="absolute left-0 top-0 bottom-0 w-2 cursor-ew-resize bg-blue-400 opacity-0 hover:opacity-100 transition-opacity" />
            <div className="absolute right-0 top-0 bottom-0 w-2 cursor-ew-resize bg-blue-400 opacity-0 hover:opacity-100 transition-opacity" />
          </>
        )}
      </div>
    </div>
  );
};
