import React from 'react';
import type { Timeline as TimelineType, VideoFile, VideoClip } from '../../types/video';
import { TimelineRuler } from './TimelineRuler';
import { TimelineTrackComponent } from './TimelineTrack';
import { ZoomIn, ZoomOut, Plus } from 'lucide-react';

interface TimelineProps {
  timeline: TimelineType;
  videoFiles: VideoFile[];
  selectedClips: string[];
  onTimelineUpdate: (updates: Partial<TimelineType>) => void;
  onClipUpdate: (clipId: string, updates: Partial<VideoClip>) => void;
  onClipDelete: (clipId: string) => void;
  onSeek: (time: number) => void;
  onSelectedClipsChange: (clipIds: string[]) => void;
  className?: string;
}

export const Timeline: React.FC<TimelineProps> = ({
  timeline,
  videoFiles,
  selectedClips,
  onTimelineUpdate,
  onClipUpdate,
  onClipDelete,
  onSeek,
  onSelectedClipsChange,
  className = ''
}) => {
  const timelineRef = React.useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = React.useState(false);

  const handleZoomIn = () => {
    const newZoom = Math.min(timeline.zoom * 1.5, 10);
    onTimelineUpdate({
      zoom: newZoom,
      pixelsPerSecond: 50 * newZoom
    });
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(timeline.zoom / 1.5, 0.1);
    onTimelineUpdate({
      zoom: newZoom,
      pixelsPerSecond: 50 * newZoom
    });
  };

  const handleAddTrack = () => {
    const newTrack = {
      id: `track-${Date.now()}`,
      name: `Video Track ${timeline.tracks.length + 1}`,
      clips: [],
      height: 80,
      locked: false,
      visible: true,
    };

    onTimelineUpdate({
      tracks: [...timeline.tracks, newTrack]
    });
  };

  const handleClipSelect = (clipId: string, multiSelect = false) => {
    if (!clipId) {
      onSelectedClipsChange([]);
      return;
    }

    if (multiSelect) {
      const newSelection = selectedClips.includes(clipId)
        ? selectedClips.filter(id => id !== clipId)
        : [...selectedClips, clipId];
      onSelectedClipsChange(newSelection);
    } else {
      onSelectedClipsChange([clipId]);
    }
  };

  const handleTrackToggleVisibility = (trackId: string) => {
    const updatedTracks = timeline.tracks.map(track =>
      track.id === trackId ? { ...track, visible: !track.visible } : track
    );
    onTimelineUpdate({ tracks: updatedTracks });
  };

  const handleTrackToggleLock = (trackId: string) => {
    const updatedTracks = timeline.tracks.map(track =>
      track.id === trackId ? { ...track, locked: !track.locked } : track
    );
    onTimelineUpdate({ tracks: updatedTracks });
  };

  // Auto-scroll to keep playhead visible
  React.useEffect(() => {
    if (timelineRef.current && !isScrolling) {
      const container = timelineRef.current;
      const playheadPosition = timeline.currentTime * timeline.pixelsPerSecond;
      const containerWidth = container.clientWidth;
      const scrollLeft = container.scrollLeft;

      if (playheadPosition < scrollLeft || playheadPosition > scrollLeft + containerWidth) {
        container.scrollLeft = Math.max(0, playheadPosition - containerWidth / 2);
      }
    }
  }, [timeline.currentTime, timeline.pixelsPerSecond, isScrolling]);

  const handleScroll = () => {
    setIsScrolling(true);
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => setIsScrolling(false), 150);
  };

  let scrollTimeout: NodeJS.Timeout;

  return (
    <div className={`bg-gray-900 border border-gray-700 rounded-lg overflow-hidden ${className}`}>
      {/* Timeline Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-gray-100">Timeline</h3>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Zoom:</span>
            <button
              onClick={handleZoomOut}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Zoom out"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            
            <span className="text-xs text-gray-400 min-w-[3rem] text-center">
              {Math.round(timeline.zoom * 100)}%
            </span>
            
            <button
              onClick={handleZoomIn}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Zoom in"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
          </div>
        </div>

        <button
          onClick={handleAddTrack}
          className="flex items-center space-x-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add Track</span>
        </button>
      </div>

      {/* Timeline Content */}
      <div className="flex flex-col">
        {/* Ruler */}
        <div className="flex">
          <div className="w-48 bg-gray-800 border-r border-gray-700" />
          <div 
            ref={timelineRef}
            className="flex-1 overflow-x-auto overflow-y-hidden"
            onScroll={handleScroll}
          >
            <TimelineRuler
              duration={timeline.duration}
              pixelsPerSecond={timeline.pixelsPerSecond}
              currentTime={timeline.currentTime}
              onSeek={onSeek}
            />
          </div>
        </div>

        {/* Tracks */}
        <div className="flex-1 max-h-96 overflow-y-auto">
          {timeline.tracks.map((track) => (
            <div key={track.id} className="flex">
              <div className="w-48" />
              <div className="flex-1 overflow-x-auto overflow-y-hidden">
                <TimelineTrackComponent
                  track={track}
                  videoFiles={videoFiles}
                  pixelsPerSecond={timeline.pixelsPerSecond}
                  currentTime={timeline.currentTime}
                  selectedClips={selectedClips}
                  onClipSelect={handleClipSelect}
                  onClipUpdate={onClipUpdate}
                  onClipDelete={onClipDelete}
                  onTrackToggleVisibility={handleTrackToggleVisibility}
                  onTrackToggleLock={handleTrackToggleLock}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Empty state */}
        {timeline.tracks.every(track => track.clips.length === 0) && (
          <div className="flex-1 flex items-center justify-center py-12 text-gray-400">
            <div className="text-center">
              <div className="text-4xl mb-4">🎬</div>
              <p className="text-lg font-medium">Timeline is empty</p>
              <p className="text-sm mt-2">Add video clips from the library to start editing</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
