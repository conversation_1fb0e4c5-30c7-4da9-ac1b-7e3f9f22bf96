import React from 'react';
import { formatTime } from '../../utils/videoUtils';

interface TimelineRulerProps {
  duration: number;
  pixelsPerSecond: number;
  currentTime: number;
  onSeek: (time: number) => void;
  className?: string;
}

export const TimelineRuler: React.FC<TimelineRulerProps> = ({
  duration,
  pixelsPerSecond,
  currentTime,
  onSeek,
  className = ''
}) => {
  const rulerRef = React.useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = React.useState(false);

  const totalWidth = duration * pixelsPerSecond;
  const playheadPosition = currentTime * pixelsPerSecond;

  // Generate time markers
  const getTimeMarkers = () => {
    const markers = [];
    const interval = duration > 60 ? 10 : duration > 30 ? 5 : 1; // Adjust interval based on duration
    
    for (let time = 0; time <= duration; time += interval) {
      const position = time * pixelsPerSecond;
      markers.push({
        time,
        position,
        label: formatTime(time),
        isMajor: time % (interval * 2) === 0
      });
    }
    
    return markers;
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    handleSeek(e);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      handleSeek(e);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleSeek = (e: React.MouseEvent) => {
    if (!rulerRef.current) return;
    
    const rect = rulerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const time = Math.max(0, Math.min(x / pixelsPerSecond, duration));
    onSeek(time);
  };

  React.useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false);
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging && rulerRef.current) {
        const rect = rulerRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const time = Math.max(0, Math.min(x / pixelsPerSecond, duration));
        onSeek(time);
      }
    };

    if (isDragging) {
      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('mousemove', handleGlobalMouseMove);
    }

    return () => {
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('mousemove', handleGlobalMouseMove);
    };
  }, [isDragging, pixelsPerSecond, duration, onSeek]);

  const timeMarkers = getTimeMarkers();

  return (
    <div className={`relative bg-gray-800 border-b border-gray-700 ${className}`}>
      <div
        ref={rulerRef}
        className="relative h-12 cursor-pointer select-none overflow-hidden"
        style={{ width: totalWidth }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      >
        {/* Time markers */}
        {timeMarkers.map((marker) => (
          <div
            key={marker.time}
            className="absolute top-0 flex flex-col items-center"
            style={{ left: marker.position }}
          >
            {/* Tick mark */}
            <div
              className={`w-px bg-gray-400 ${
                marker.isMajor ? 'h-4' : 'h-2'
              }`}
            />
            
            {/* Time label */}
            {marker.isMajor && (
              <span className="text-xs text-gray-300 mt-1 whitespace-nowrap">
                {marker.label}
              </span>
            )}
          </div>
        ))}

        {/* Playhead */}
        <div
          className="absolute top-0 w-0.5 h-full bg-red-500 z-10 pointer-events-none"
          style={{ left: playheadPosition }}
        >
          {/* Playhead handle */}
          <div className="absolute -top-1 -left-2 w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-lg" />
        </div>

        {/* Current time indicator */}
        <div
          className="absolute -top-8 bg-red-500 text-white text-xs px-2 py-1 rounded pointer-events-none z-20"
          style={{ 
            left: Math.max(0, Math.min(playheadPosition - 30, totalWidth - 60)),
            transform: 'translateX(0)'
          }}
        >
          {formatTime(currentTime)}
        </div>
      </div>
    </div>
  );
};
