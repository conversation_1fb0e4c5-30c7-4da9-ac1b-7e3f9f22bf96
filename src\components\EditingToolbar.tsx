import React from 'react';
import { Sc<PERSON><PERSON>, <PERSON>, Trash2, <PERSON><PERSON>, Undo, Redo, Save } from 'lucide-react';
import type { VideoClip, Timeline } from '../types/video';

interface EditingToolbarProps {
  timeline: Timeline;
  selectedClips: string[];
  onCutAtPlayhead: () => void;
  onSplitClip: (clipId: string) => void;
  onDeleteSelected: () => void;
  onCopySelected: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  canUndo: boolean;
  canRedo: boolean;
  className?: string;
}

export const EditingToolbar: React.FC<EditingToolbarProps> = ({
  timeline,
  selectedClips,
  onCutAtPlayhead,
  onSplitClip,
  onDeleteSelected,
  onCopySelected,
  onUndo,
  onRedo,
  onSave,
  canUndo,
  canRedo,
  className = ''
}) => {
  const hasSelection = selectedClips.length > 0;
  const singleSelection = selectedClips.length === 1;

  const handleSplitSelected = () => {
    if (singleSelection) {
      onSplitClip(selectedClips[0]);
    }
  };

  return (
    <div className={`bg-gray-800 border-b border-gray-700 p-3 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Left side - Main editing tools */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 border-r border-gray-600 pr-3">
            <button
              onClick={onUndo}
              disabled={!canUndo}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Undo (Ctrl+Z)"
            >
              <Undo className="w-4 h-4" />
            </button>
            
            <button
              onClick={onRedo}
              disabled={!canRedo}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Redo (Ctrl+Y)"
            >
              <Redo className="w-4 h-4" />
            </button>
          </div>

          <div className="flex items-center space-x-1 border-r border-gray-600 pr-3">
            <button
              onClick={onCutAtPlayhead}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Cut at playhead (C)"
            >
              <Scissors className="w-4 h-4" />
            </button>
            
            <button
              onClick={handleSplitSelected}
              disabled={!singleSelection}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Split selected clip (S)"
            >
              <Split className="w-4 h-4" />
            </button>
          </div>

          <div className="flex items-center space-x-1">
            <button
              onClick={onCopySelected}
              disabled={!hasSelection}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Copy selected (Ctrl+C)"
            >
              <Copy className="w-4 h-4" />
            </button>
            
            <button
              onClick={onDeleteSelected}
              disabled={!hasSelection}
              className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Delete selected (Delete)"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Center - Selection info */}
        <div className="text-sm text-gray-400">
          {hasSelection ? (
            <span>
              {selectedClips.length} clip{selectedClips.length !== 1 ? 's' : ''} selected
            </span>
          ) : (
            <span>No clips selected</span>
          )}
        </div>

        {/* Right side - Project actions */}
        <div className="flex items-center space-x-2">
          <button
            onClick={onSave}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
            title="Save project (Ctrl+S)"
          >
            <Save className="w-4 h-4" />
            <span>Save</span>
          </button>
        </div>
      </div>

      {/* Keyboard shortcuts hint */}
      <div className="mt-2 text-xs text-gray-500">
        <span className="mr-4">C: Cut</span>
        <span className="mr-4">S: Split</span>
        <span className="mr-4">Del: Delete</span>
        <span className="mr-4">Ctrl+Z: Undo</span>
        <span className="mr-4">Ctrl+Y: Redo</span>
        <span>Space: Play/Pause</span>
      </div>
    </div>
  );
};
