import type { VideoMetadata } from '../types/video';

export const getVideoMetadata = (file: File): Promise<VideoMetadata> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.preload = 'metadata';
    
    video.onloadedmetadata = () => {
      resolve({
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        frameRate: 30, // Default, would need more complex detection for actual frame rate
      });
      URL.revokeObjectURL(video.src);
    };
    
    video.onerror = () => {
      reject(new Error('Failed to load video metadata'));
      URL.revokeObjectURL(video.src);
    };
    
    video.src = URL.createObjectURL(file);
  });
};

export const generateVideoThumbnails = (
  file: File,
  count: number = 10
): Promise<string[]> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }
    
    video.preload = 'metadata';
    
    video.onloadedmetadata = () => {
      canvas.width = 160;
      canvas.height = 90;
      
      const duration = video.duration;
      const interval = duration / count;
      const thumbnails: string[] = [];
      let currentIndex = 0;
      
      const captureFrame = () => {
        if (currentIndex >= count) {
          resolve(thumbnails);
          URL.revokeObjectURL(video.src);
          return;
        }
        
        video.currentTime = currentIndex * interval;
      };
      
      video.onseeked = () => {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        thumbnails.push(canvas.toDataURL('image/jpeg', 0.7));
        currentIndex++;
        captureFrame();
      };
      
      captureFrame();
    };
    
    video.onerror = () => {
      reject(new Error('Failed to generate thumbnails'));
      URL.revokeObjectURL(video.src);
    };
    
    video.src = URL.createObjectURL(file);
  });
};

export const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 100);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
};

export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export const isVideoFile = (file: File): boolean => {
  return file.type.startsWith('video/');
};

export const getSupportedVideoFormats = (): string[] => {
  return [
    'video/mp4',
    'video/mov',
    'video/quicktime',
    'video/webm',
    'video/avi',
    'video/mkv',
  ];
};
