import React from 'react';
import type { VideoFile } from '../types/video';
import { formatTime, formatFileSize } from '../utils/videoUtils';
import { Play, Trash2, Plus } from 'lucide-react';

interface VideoLibraryProps {
  videoFiles: VideoFile[];
  onRemoveVideo: (fileId: string) => void;
  onAddToTimeline: (fileId: string) => void;
  className?: string;
}

export const VideoLibrary: React.FC<VideoLibraryProps> = ({
  videoFiles,
  onRemoveVideo,
  onAddToTimeline,
  className = ''
}) => {
  if (videoFiles.length === 0) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <p className="text-gray-500 dark:text-gray-400">
          No videos uploaded yet. Upload some videos to get started.
        </p>
      </div>
    );
  }

  return (
    <div className={`p-4 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        Video Library ({videoFiles.length})
      </h3>
      
      <div className="space-y-3">
        {videoFiles.map((video) => (
          <div
            key={video.id}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start space-x-4">
              {/* Thumbnail */}
              <div className="flex-shrink-0">
                <div className="w-20 h-12 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden">
                  {video.thumbnails.length > 0 && (
                    <img
                      src={video.thumbnails[0]}
                      alt={video.name}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
              </div>

              {/* Video Info */}
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {video.name}
                </h4>
                <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 space-y-1">
                  <div>Duration: {formatTime(video.duration)}</div>
                  <div>Resolution: {video.width} × {video.height}</div>
                  <div>Size: {formatFileSize(video.size)}</div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex-shrink-0 flex items-center space-x-2">
                <button
                  onClick={() => onAddToTimeline(video.id)}
                  className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                  title="Add to timeline"
                >
                  <Plus className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => onRemoveVideo(video.id)}
                  className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                  title="Remove video"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
