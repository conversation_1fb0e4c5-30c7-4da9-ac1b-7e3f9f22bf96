import React, { useEffect, useRef } from 'react';
import { Play, Pause, SkipBack, SkipForward, Volume2 } from 'lucide-react';
import type { Timeline, VideoFile } from '../types/video';
import { formatTime } from '../utils/videoUtils';

interface VideoPreviewProps {
  timeline: Timeline;
  videoFiles: VideoFile[];
  isPlaying: boolean;
  onPlay: () => void;
  onPause: () => void;
  onSeek: (time: number) => void;
  onTimeUpdate: (time: number) => void;
  className?: string;
}

export const VideoPreview: React.FC<VideoPreviewProps> = ({
  timeline,
  videoFiles,
  isPlaying,
  onPlay,
  onPause,
  onSeek,
  onTimeUpdate,
  className = ''
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [volume, setVolume] = React.useState(1);
  const [isMuted, setIsMuted] = React.useState(false);

  // Get the current clip at the timeline position
  const getCurrentClip = () => {
    for (const track of timeline.tracks) {
      for (const clip of track.clips) {
        if (timeline.currentTime >= clip.position && 
            timeline.currentTime < clip.position + clip.duration) {
          return clip;
        }
      }
    }
    return null;
  };

  const currentClip = getCurrentClip();
  const currentVideoFile = currentClip 
    ? videoFiles.find(file => file.id === currentClip.videoFileId)
    : null;

  // Update video source when clip changes
  useEffect(() => {
    if (videoRef.current && currentVideoFile) {
      videoRef.current.src = currentVideoFile.url;
      
      // Set the video time based on clip timing
      if (currentClip) {
        const relativeTime = timeline.currentTime - currentClip.position;
        const videoTime = currentClip.startTime + relativeTime;
        videoRef.current.currentTime = videoTime;
      }
    }
  }, [currentVideoFile, currentClip, timeline.currentTime]);

  // Handle play/pause
  useEffect(() => {
    if (videoRef.current) {
      if (isPlaying && currentVideoFile) {
        videoRef.current.play();
      } else {
        videoRef.current.pause();
      }
    }
  }, [isPlaying, currentVideoFile]);

  // Handle time updates
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      if (currentClip) {
        const videoTime = video.currentTime;
        const timelineTime = currentClip.position + (videoTime - currentClip.startTime);
        onTimeUpdate(timelineTime);
      }
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    return () => video.removeEventListener('timeupdate', handleTimeUpdate);
  }, [currentClip, onTimeUpdate]);

  const handleSkipBack = () => {
    onSeek(Math.max(0, timeline.currentTime - 10));
  };

  const handleSkipForward = () => {
    onSeek(Math.min(timeline.duration, timeline.currentTime + 10));
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
    }
  };

  return (
    <div className={`bg-black rounded-lg overflow-hidden ${className}`}>
      {/* Video Display */}
      <div className="relative aspect-video bg-gray-900 flex items-center justify-center">
        {currentVideoFile ? (
          <video
            ref={videoRef}
            className="w-full h-full object-contain"
            muted={isMuted}
            volume={volume}
          />
        ) : (
          <div className="text-gray-400 text-center">
            <div className="text-6xl mb-4">🎬</div>
            <p>No video selected</p>
            <p className="text-sm mt-2">Add videos to the timeline to preview</p>
          </div>
        )}
        
        {/* Overlay Controls */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
          <button
            onClick={isPlaying ? onPause : onPlay}
            className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-4 transition-all"
            disabled={!currentVideoFile}
          >
            {isPlaying ? (
              <Pause className="w-8 h-8 text-white" />
            ) : (
              <Play className="w-8 h-8 text-white ml-1" />
            )}
          </button>
        </div>
      </div>

      {/* Control Bar */}
      <div className="bg-gray-800 p-4">
        <div className="flex items-center justify-between">
          {/* Playback Controls */}
          <div className="flex items-center space-x-3">
            <button
              onClick={handleSkipBack}
              className="p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors"
              disabled={!currentVideoFile}
            >
              <SkipBack className="w-5 h-5" />
            </button>
            
            <button
              onClick={isPlaying ? onPause : onPlay}
              className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors"
              disabled={!currentVideoFile}
            >
              {isPlaying ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5 ml-0.5" />
              )}
            </button>
            
            <button
              onClick={handleSkipForward}
              className="p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors"
              disabled={!currentVideoFile}
            >
              <SkipForward className="w-5 h-5" />
            </button>
          </div>

          {/* Time Display */}
          <div className="text-gray-300 text-sm font-mono">
            {formatTime(timeline.currentTime)} / {formatTime(timeline.duration)}
          </div>

          {/* Volume Control */}
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleMute}
              className="p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors"
            >
              <Volume2 className="w-5 h-5" />
            </button>
            
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={isMuted ? 0 : volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-20 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
