import { Composition } from 'remotion';
import { VideoComposition } from './VideoComposition';

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="VideoEditor"
        component={VideoComposition}
        durationInFrames={3000} // 100 seconds at 30fps
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{
          timeline: {
            tracks: [],
            duration: 0,
            currentTime: 0,
            zoom: 1,
            pixelsPerSecond: 50,
          },
          videoFiles: [],
        }}
      />
    </>
  );
};
