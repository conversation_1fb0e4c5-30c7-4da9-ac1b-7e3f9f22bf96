export interface VideoFile {
  id: string;
  file: File;
  name: string;
  duration: number;
  width: number;
  height: number;
  frameRate: number;
  size: number;
  url: string;
  thumbnails: string[];
  createdAt: Date;
}

export interface VideoClip {
  id: string;
  videoFileId: string;
  name: string;
  startTime: number;
  endTime: number;
  trimStart: number;
  trimEnd: number;
  position: number; // Position on timeline in seconds
  trackIndex: number;
  duration: number;
  originalDuration: number;
  thumbnails: string[];
}

export interface TimelineTrack {
  id: string;
  name: string;
  clips: VideoClip[];
  height: number;
  locked: boolean;
  visible: boolean;
}

export interface Timeline {
  tracks: TimelineTrack[];
  duration: number;
  currentTime: number;
  zoom: number;
  pixelsPerSecond: number;
}

export interface VideoMetadata {
  duration: number;
  width: number;
  height: number;
  frameRate: number;
  bitrate?: number;
  codec?: string;
}

export interface EditOperation {
  id: string;
  type: 'cut' | 'trim' | 'split' | 'move' | 'delete';
  clipId: string;
  timestamp: number;
  data: any;
}

export interface ExportSettings {
  width: number;
  height: number;
  frameRate: number;
  bitrate: number;
  format: 'mp4' | 'mov' | 'webm';
  quality: 'low' | 'medium' | 'high' | 'ultra';
}
